{"name": "cybersecurity-awareness-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@headlessui/react": "^1.7.17", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.6.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.9.1", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "chart.js": "^4.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dompurify": "^3.0.5", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.292.0", "next": "14.0.0", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "nodemailer": "^6.9.7", "prisma": "^5.6.0", "react": "^18", "react-chartjs-2": "^5.2.0", "react-confetti": "^6.4.0", "react-dom": "^18", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.4.1", "react-particles": "^2.12.2", "recharts": "^2.8.0", "sonner": "^2.0.7", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "tsparticles-slim": "^2.12.0", "zod": "^3.22.4", "zustand": "^5.0.7"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/dompurify": "^3.0.5", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "tsx": "^4.0.0", "typescript": "^5"}}